<script setup lang="ts" name="user-view">
import { useVbenDrawer } from '@vben/common-ui';

const data = ref<any>({});
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  title: '详情',
  showCancelButton: false,
  class: 'w-1/2',
  onConfirm: () => {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const _data = drawerApi.getData<Record<string, any>>();
      Object.keys(_data).forEach((key) => {
        data.value[key] = _data[key] ? _data[key] : '-';
      });
    }
  },
});
</script>
<template>
  <Drawer>
    <a-descriptions label-placement="left" :column="2" size="small" bordered>
      <a-descriptions-item label="名称">{{ data.mc }}</a-descriptions-item>
      <a-descriptions-item label="中文拼音">
        {{ data.zwpy }}
      </a-descriptions-item>
      <a-descriptions-item label="五笔拼音">
        {{ data.wbpy }}
      </a-descriptions-item>
      <a-descriptions-item label="单位机构代码">
        {{ data.dwjgdm }}
      </a-descriptions-item>
      <a-descriptions-item label="区划代码">
        {{ data.qhdm }}
      </a-descriptions-item>
      <a-descriptions-item label="单位级别">
        {{ data.dwjbLabel }}
      </a-descriptions-item>
      <a-descriptions-item label="备注">{{ data.bz }}</a-descriptions-item>
      <a-descriptions-item label="启用标志">
        {{ data.qybzLabel }}
      </a-descriptions-item>
      <a-descriptions-item label="变动类型">
        {{ data.bdlx }}
      </a-descriptions-item>
      <a-descriptions-item label="变动时间">
        {{ data.bdsj }}
      </a-descriptions-item>
      <a-descriptions-item label="分局机构代码">
        {{ data.fjjgdm }}
      </a-descriptions-item>
      <a-descriptions-item label="分局机构名称">
        {{ data.fjjgmc }}
      </a-descriptions-item>
      <a-descriptions-item label="上级单位代码">
        {{ data.sjdwdm }}
      </a-descriptions-item>
      <a-descriptions-item label="收件人姓名">
        {{ data.sjrxm }}
      </a-descriptions-item>
      <a-descriptions-item label="收件人联系电话">
        {{ data.sjrlxdh }}
      </a-descriptions-item>
      <a-descriptions-item label="收件地址">
        {{ data.sjdz }}
      </a-descriptions-item>
      <a-descriptions-item label="收件邮编">
        {{ data.sjyb }}
      </a-descriptions-item>
      <a-descriptions-item label="收件人省市县区">
        {{ data.sjrssxq }}
      </a-descriptions-item>
      <a-descriptions-item label="收件人详址">
        {{ data.sjrxz }}
      </a-descriptions-item>
      <a-descriptions-item label="地址编码">
        {{ data.dzbm }}
      </a-descriptions-item>
      <a-descriptions-item label="上级地址编码">
        {{ data.sjdzbm }}
      </a-descriptions-item>
      <a-descriptions-item label="政务大厅编号">
        {{ data.zwdtbh }}
      </a-descriptions-item>
      <a-descriptions-item label="政务大厅名称">
        {{ data.zwdtmc }}
      </a-descriptions-item>
      <a-descriptions-item label="单位固定电话">
        {{ data.gddh }}
      </a-descriptions-item>
    </a-descriptions>
  </Drawer>
</template>
<style lang="less" scoped></style>
