<script setup lang="ts" name="user-view">
import { useVbenDrawer } from '@vben/common-ui';

const data = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  title: '详情',
  showCancelButton: false,
  class: 'w-1/2',
  onConfirm: () => {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});
</script>
<template>
  <Drawer>
    <a-descriptions label-placement="left" :column="2" size="small" bordered>
      <a-descriptions-item label="用户登录名">
        {{ data.yhdlm }}
      </a-descriptions-item>
      <a-descriptions-item label="警员号">{{ data.jyh }}</a-descriptions-item>
      <a-descriptions-item label="用户姓名">
        {{ data.yhxm }}
      </a-descriptions-item>
      <a-descriptions-item label="中文查询拼音">
        {{ data.zwpy }}
      </a-descriptions-item>
      <a-descriptions-item label="用户性别">
        {{ data.yhxbLabel }}
      </a-descriptions-item>
      <a-descriptions-item label="身份号码">
        {{ data.gmsfhm }}
      </a-descriptions-item>
      <a-descriptions-item label="用户职务">
        {{ data.yhzwLabel }}
      </a-descriptions-item>
      <a-descriptions-item label="所属公安机关">
        {{ data.gajgjgdm }}
      </a-descriptions-item>
      <a-descriptions-item label="联系电话">
        {{ data.lxdh }}
      </a-descriptions-item>
      <a-descriptions-item label="短信通知号码">
        {{ data.dxtzhm }}
      </a-descriptions-item>
      <a-descriptions-item label="受理单位">
        {{ data.dwdmLabel }}
      </a-descriptions-item>
      <a-descriptions-item label="备注1">{{ data.bz1 }}</a-descriptions-item>
      <a-descriptions-item label="备注2">{{ data.bz2 }}</a-descriptions-item>
    </a-descriptions>
  </Drawer>
</template>
<style lang="less" scoped></style>
