import type {
  BaseFormComponentType,
  ExtendedFormApi,
  VbenFormSchema,
} from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { z } from '@vben/common-ui';

import pinyin from 'js-pinyin';

import { apixtJsxxbPage } from '#/api';

pinyin.setOptions({ charCase: 2, checkPolyphone: false });

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        minLevel: 12,
        dataType: 'ga',
        glmType: 'cxglm',
        ssxq: '',
      },
    },
    fieldName: 'dwdm',
    label: '单位',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'yhdlmLike',
    label: '用户登录名',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'yhxm',
    label: '用户姓名',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: 9007,
      },
    },
    fieldName: 'yhzt',
    label: '用户状态',
  },
];

// 表格列数据
export function useColumns<T = any>(
  onActionClick: T,
): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'yhdlm',
      title: '用户登录名',
    },
    {
      field: 'yhxm',
      title: '用户姓名',
    },
    {
      title: '警员号',
      field: 'jyh',
    },
    { title: '受理单位', field: 'dwdmLabel' },
    { title: '中文拼音', field: 'zwpy' },
    { title: '用户性别', field: 'yhxbLabel' },
    { title: '用户职务', field: 'yhzwLabel' },
    { title: '用户密级', field: 'yhmjLabel' },
    { title: '操作密级', field: 'czmj' },
    { title: '用户状态', field: 'yhztLabel' },
    { title: '公民身份号码', field: 'gmsfhm', minWidth: 120 },
    { title: '联系电话', field: 'lxdh' },
    { title: '公安机关机构代码', field: 'gajgjgdm', minWidth: 120 },
    { title: '公安机关机构', field: 'gajgjgdmLabel' },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          'view', // 默认的详情按钮
          'edit', // 默认的编辑按钮
          {
            code: 'delete',
            text: '注销',
            show: (row: any) => row.yhzt !== '2',
          }, // 默认的删除按钮
          {
            code: 'recover',
            text: '恢复',
            show: (row: any) => row.yhzt === '2',
            confirm: true,
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      // showOverflow: false,
      title: '操作',
      width: 160,
    },
  ];
}

export const createEditFormSchema = (
  cb: () => ExtendedFormApi,
): VbenFormSchema<BaseFormComponentType>[] => [
  {
    title: '基础信息',
    fieldName: 'titleSlot',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户登录名',
    },
    fieldName: 'yhdlm',
    label: '用户登录名',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户姓名',
      onChange: (e: any) => {
        const form = cb();
        form.setFieldValue('zwpy', pinyin.getCamelChars(e.target.value));
      },
    },
    fieldName: 'yhxm',
    label: '用户姓名',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '自动生成',
    },
    fieldName: 'zwpy',
    label: '中文查询拼音',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入身份号码',
      maxlength: 18,
      inputType: 'gmsfhm',
      message: '请输入正确的公民身份号码',
    },
    fieldName: 'gmsfhm',
    label: '身份号码',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入登录口令',
      type: 'password',
    },
    fieldName: 'dlkl',
    label: '登录口令',
    rules: z.string().min(6, '密码长度不低于6位'),
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入口令确认',
      type: 'password',
    },
    fieldName: 'klqr',
    label: '口令确认',
    rules: z.string().refine(
      async () => {
        const { dlkl, klqr } = await cb().getValues();
        return dlkl === klqr;
      },
      {
        message: '两次密码不一致',
        path: ['klqr'], // 错误信息关联到 confirmPassword 字段
      },
    ),
  },
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        ssxq: '',
        glmType: 'cxglm',
        minLevel: '12',
        dataType: 'dwjg',
      },
    },
    fieldName: 'gajgjgdm',
    label: '所属公安机关',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入联系电话',
    },
    fieldName: 'lxdh',
    label: '联系电话',
    rules: 'required',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        ssxq: '',
        glmType: 'cxglm',
        minLevel: '9',
        dataType: 'ga',
      },
    },
    fieldName: 'dwdm',
    label: '受理单位',
    help: '证件分拣投递单位',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入警员号',
    },
    fieldName: 'jyh',
    label: '警员号',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      params: {
        type: '8003',
      },
    },
    fieldName: 'yhxb',
    label: '用户性别',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      params: {
        type: '9009',
      },
    },
    fieldName: 'yhzw',
    label: '用户职务',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户类型',
    },
    fieldName: 'yhlx',
    label: '用户类型',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入短信通知号码',
    },
    fieldName: 'dxtzhm',
    label: '短信通知号码',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入备注1',
    },
    fieldName: 'bz1',
    label: '备注1',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入备注2',
    },
    fieldName: 'bz2',
    label: '备注2',
  },
  {
    title: '角色、数据范围、IP地址',
    fieldName: 'titleSlot',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      mode: 'multiple',
      maxTagTextLength: 7,
      api: () =>
        apixtJsxxbPage({
          pageNumber: 1,
          pageSize: 100,
        }),
      afterFetch: async (result: any) => {
        return result.records.map(({ jsid, jsmc, ...others }: any) => {
          return {
            ...others,
            text: jsmc,
            value: jsid,
          };
        });
      },
    },
    fieldName: 'jsidList',
    label: '角色',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        ssxq: [],
        glmType: 'cxglm',
        minLevel: '12',
        dataType: 'ga',
      },
      // maxTagCount: 1,
      multiple: true,
    },
    fieldName: 'sjfwList',
    label: '数据范围',
  },
  {
    title: '操作密级',
    fieldName: 'titleSlot',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      params: {
        type: '9010',
      },
    },
    fieldName: 'yhmj',
    label: '用户密级',
    help: '原WEB使用',
  },
  {
    component: 'CheckboxGroup',
    componentProps: {
      options: [
        {
          label: '系统管理员',
          value: 1000,
        },
        {
          label: '上报数据专管员',
          value: 100,
        },
        {
          label: '制证专管员',
          value: 10,
        },
        {
          label: '一般用户',
          value: 1,
        },
      ],
    },
    // labelClass: 'bg-red-500',
    formItemClass: 'md:col-span-1 lg:col-span-2',
    fieldName: 'czmj',
    label: '操作密级',
    help: '原WEB使用',
  },
];

// export const createEditFormSchema = (
//   cb: () => ExtendedFormApi,
// ): VbenFormSchema<BaseFormComponentType>[] => [
//   {
//     title: '基础信息',
//     fieldName: 'titleSlot',
//   },
//   {
//     component: 'ApiCascader',
//     componentProps: {
//       params: {
//         ssxq: '',
//         glmType: 'cxglm',
//         minLevel: '12',
//         dataType: 'ga',
//       },
//       multiple: true,
//     },
//     fieldName: 'sjfwList',
//     label: '数据范围',
//     rules: 'required',
//   },
// ];
