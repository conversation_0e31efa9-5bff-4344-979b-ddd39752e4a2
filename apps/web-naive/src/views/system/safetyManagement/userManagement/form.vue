<script setup lang="ts" name="user-view">
import type { ExtendedVxeGridApi } from 'node_modules/@vben/plugins/src/vxe-table/types';

import type { FormType } from '#/adapter/form';

import { useVbenDrawer, useVbenForm, z } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { apiXtYhxxCreate, apiXtYhxxUpdate } from '#/api';

import { createEditFormSchema } from './data';

type dataType = {
  [key: string]: any;
  FormType: FormType;
  gridApi?: ExtendedVxeGridApi;
  isEdit: boolean;
};
const data = ref<dataType>({
  FormType: 'create',
  isEdit: false,
});
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  class: 'w-2/3',
  onCancel: () => {
    drawerApi.close();
  },
  onConfirm: async () => {
    const values: any = await formApi.validateAndSubmitForm();
    if (!values) return;
    const { gridApi, ...others } = data.value;
    drawerApi.lock();
    drawerApi.setState({ confirmLoading: true });
    const czmj: any = [0, 0, 0, 0];
    values.czmj.forEach((v: number) => {
      const log = Math.log10(v);
      czmj[3 - log] = '1';
    });
    values.czmj = czmj.map((v: any) => (v === '1' ? '1' : '0')).join('');
    const sjfwList = values.sjfwList?.map((v: any) => {
      return v[v.length - 1];
    });
    await (data.value.isEdit ? apiXtYhxxUpdate : apiXtYhxxCreate)({
      ...others,
      ...values,
      ...formatCascaderValue(values, ['gajgjgdm', 'dwdm']),
      sjfwList,
    })
      .then(() => {
        message.success(data.value.isEdit ? '更新成功' : '创建成功');
        drawerApi.close();
        gridApi?.query();
      })
      .finally(() => {
        drawerApi.unlock();
        drawerApi.setState({ confirmLoading: false });
      });
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = await drawerApi.getData();
      data.value.isEdit = data.value.FormType === 'edit';
      data.value.czmj = data.value.isEdit
        ? [...data.value.czmj].map((v, i) => {
            return v === '1' ? 10 ** (3 - i) : undefined;
          })
        : [undefined, undefined, undefined, undefined];
      const title = data.value.isEdit ? '编辑' : '新增';
      drawerApi.setState({
        title,
      });
      formApi.setValues({ ...data.value });
      if (data.value.isEdit) {
        formApi.updateSchema([
          {
            component: 'ApiCascader',
            componentProps: {
              params: {
                ssxq: data.value.gajgjgdm,
                glmType: 'cxglm',
                minLevel: '12',
                dataType: 'dwjg',
              },
              formApi,
            },
            fieldName: 'gajgjgdm',
            label: '所属公安机关',
            rules: 'required',
          },
          {
            component: 'ApiCascader',
            componentProps: {
              params: {
                ssxq: data.value.dwdm,
                glmType: 'cxglm',
                minLevel: '9',
                dataType: 'ga',
              },
              formApi,
            },
            fieldName: 'dwdm',
            label: '受理单位',
            help: '证件分拣投递单位',
            rules: 'required',
          },
          {
            component: 'ApiCascader',
            componentProps: {
              params: {
                ssxq: data.value.sjfwList,
                glmType: 'cxglm',
                minLevel: '12',
                dataType: 'ga',
              },
              multiple: true,
              formApi,
            },
            fieldName: 'sjfwList',
            label: '数据范围',
          },
          {
            fieldName: 'dlkl',
            rules: z
              .union([z.literal(''), z.string().min(6, '密码长度不低于6位')])
              .optional(),
          },
          {
            fieldName: 'klqr',
            rules: z
              .string()
              .optional()
              .refine(
                async () => {
                  const { dlkl, klqr } = data.value;
                  return dlkl === klqr;
                },
                {
                  message: '两次密码不一致',
                  path: ['klqr'], // 错误信息关联到 confirmPassword 字段
                },
              ),
          },
        ]);
      }
    }
  },
});
const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  // 提交函数
  ...defaultFormConfig,
  submitOnEnter: false,
  showDefaultActions: false,
  schema: createEditFormSchema((): any => formApi),
});
</script>
<template>
  <Drawer>
    <Form />
  </Drawer>
</template>
<style lang="less" scoped></style>
