import type { Recordable } from '@vben/types';

import type { pageResp } from '.';

import { requestClient } from '../request';

/**
 * 用户信息列表
 * @dwdm 单位代码
 * @yhdlmLike 用户登录名
 * @yhxm 用户姓名
 * @yhzt 用户状态
 */
export interface XtYhxxbPageResp {
  // 用户ID
  yhid: number;
  // 用户登录名
  yhdlm: string;
  // 警员号
  jyh: string;
  // 单位代码
  dwdm: string;
  // 用户姓名
  yhxm: string;
  // 用户性别 (字典值: DM_XB)
  yhxb: string;
  // 用户职务 (字典值: DM_YHZW)
  yhzw: string;
  // 用户类型
  yhlx: string;
  // 用户密级 (字典值: DM_YHMJ)
  yhmj: string;
  // 操作密级
  czmj: string;
  // 用户状态 (字典值: DM_YHZT)
  yhzt: string;
  // 公民身份号码
  gmsfhm: string;
  // 中文拼音
  zwpy: string;
  // 更新时间 (格式化的日期字符串)
  gxsj: string;
  // 同步标志
  tbbz: string;
  // 包文编号
  bwbh: string;
  // 联系电话
  lxdh: string;
  // 短信通知号码
  dxtzhm: string;
  // 公安机关机构代码 (字典值: DM_GAJGJGDM)
  gajgjgdm: string;
  // 评价器设置
  pjqsz: string;
  // 备注1
  bz1: string;
  // 备注2
  bz2: string;
  // 评价器工号
  pjqgh?: string;
  // 评价器岗位
  pjqgw?: string;
  // 评价器星级
  pjqxj?: string;
  // 评价器照片ID
  pjqzpid?: number;
}
export const apiXtYhxxbPage = async (params?: {
  dwdm: string;
  yhdlmLike: string;
  yhxm: string;
  yhzt: number;
}): Promise<pageResp<XtYhxxbPageResp>> => {
  return requestClient.get('/xtYhxxb/page', {
    params,
  });
};

export const apiXtYhxxbView = async (params?: {
  id: number | string;
}): Promise<XtYhxxbPageResp> => {
  return requestClient.get('/xtYhxxb/view', {
    params,
  });
};

export const apiXtYhxxCreate = async (data: any): Promise<Recordable<any>> => {
  return requestClient.post('/xtYhxxb/create', {
    ...data,
  });
};

export const apiXtYhxxUpdate = async (data: any): Promise<Recordable<any>> => {
  return requestClient.post('/xtYhxxb/update', {
    ...data,
  });
};

export const apiXtYhxxDelete = async (data: any): Promise<Recordable<any>> => {
  return requestClient.delete('/xtYhxxb/delete', {
    data,
  });
};

export const apiXtYhxxRecover = async (
  yhid: number,
): Promise<Recordable<any>> => {
  return requestClient.post('/xtYhxxb/recover', {
    yhid,
  });
};
